import { 
  useState, useEffect,
} from "react";
import { useSession } from "next-auth/react";

export interface ButtonDistribution {
  watchlist: number;
  rateShowcase: number;
  total: number;
  watchlistPercentage: number;
  rateShowcasePercentage: number;
}

interface ButtonDistributionData {
  distribution: ButtonDistribution;
  isLoading: boolean;
  error: string | null;
}

export const useButtonDistribution = (
  slug?: string,
): ButtonDistributionData => {
  const { 
    data: session, status,
  } = useSession();
  const [distribution, setDistribution] = useState<ButtonDistribution>({
    watchlist: 0,
    rateShowcase: 0,
    total: 0,
    watchlistPercentage: 0,
    rateShowcasePercentage: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDistribution = async () => {
      if (typeof window === "undefined") {
        return true;
      }

      const isAuthenticated =
        status === "authenticated" && session?.user?.role === "subscriber";

      if (isAuthenticated) {
        setDistribution({
          watchlist: 0,
          rateShowcase: 0,
          total: 0,
          watchlistPercentage: 0,
          rateShowcasePercentage: 0,
        });
        setIsLoading(false);
        setError(null);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        const response = await fetch("/api/mixpanel/button-distribution", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            from_date: "2024-01-01",
            to_date: new Date().toISOString().split("T")[0],
            ...(slug && { slug }),
          }),
        });

        if (!response.ok) {
          throw new Error("Failed to fetch button distribution from API");
        }

        const data = await response.json();
        setDistribution(data.distribution);
      } catch (err) {
        console.error("Failed to fetch button distribution:", err);
        setError(
          err instanceof Error
            ? err.message
            : "Failed to fetch button distribution",
        );

        setDistribution({
          watchlist: 0,
          rateShowcase: 0,
          total: 0,
          watchlistPercentage: 0,
          rateShowcasePercentage: 0,
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (status !== "loading") {
      fetchDistribution();
    }
  }, [slug, status, session?.user?.role]);

  return {
    distribution,
    isLoading,
    error,
  };
};
